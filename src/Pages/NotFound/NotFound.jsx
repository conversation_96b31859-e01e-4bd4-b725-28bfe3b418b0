import React from "react";
import { useNavigate } from "react-router-dom";
import { HomeIcon, ArrowLeftIcon } from "@heroicons/react/24/outline";
import { useAuth } from "../../Context/AuthContext";

const NotFound = () => {
  const navigate = useNavigate();
  const { isLoggedIn } = useAuth();

  const handleGoHome = () => {
    if (isLoggedIn) {
      navigate("/metal-price");
    } else {
      navigate("/login");
    }
  };

  const handleGoBack = () => {
    // Simple approach: if user is logged in, try to go back to previous page
    // If not logged in or if there's no valid history, go to appropriate fallback
    if (isLoggedIn) {
      // For logged in users, try to go back, but fallback to dashboard
      if (window.history.length > 1) {
        navigate(-1);
      } else {
        navigate("/metal-price");
      }
    } else {
      // For non-logged in users, always go to login
      navigate("/login");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        {/* 404 Number */}
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-gray-300 mb-4">404</h1>
          <div className="w-24 h-1 bg-blue-500 mx-auto mb-8"></div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            Page Not Found
          </h2>
          <p className="text-gray-600 text-lg mb-2">
            Oops! The page you're looking for doesn't exist.
          </p>
          <p className="text-gray-500 text-sm">
            It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={handleGoHome}
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <HomeIcon className="w-5 h-5 mr-2" />
            {isLoggedIn ? "Go to Dashboard" : "Go to Login"}
          </button>

          <button
            onClick={handleGoBack}
            className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            {isLoggedIn ? "Go Back" : "Go to Login"}
          </button>
        </div>

        {/* Additional Help */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-gray-500 text-sm">
            If you believe this is an error, please contact support or try refreshing the page.
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
