import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import Sidebar from "../../Components/Sidebar/Sidebar";
import Dashboard from "../../Dasboard/Dashboard";
import { useAuth } from "../../Context/AuthContext";
import MetalPriceCalculation from "../MetalPriceCalculation/MetalPriceCalculation";
import MakingCharges from "../MakingCharges/MakingCharges";
import DiamondPrice from "../StuddedDiamondPriceCalculation/DiamondPrice";
import SolitairePrice from '../SolitairePriceCalculation/SolitairePrice';
import GemstonePrice from '../GemstonePriceCalculation/GemstonePrice';
import MinimumCharges from '../MinimumCharges/MinimumCharges';
import Header from "../../Components/Header/Header";
import Login from "../Login/Login";
import ProtectedRoute from "../../Components/ProtectedRoute/ProtectedRoute";
import Loader from "../../Components/Loader/Loader";
import NotFound from "../NotFound/NotFound";

const Layout = () => {
    const { isSidebarOpen, isLoggedIn, isLoading } = useAuth();

    // Show loading spinner while checking authentication
    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <Loader />
            </div>
        );
    }

    return (
        <Routes>
            {/* Public route - Login */}
            <Route
                path="/login"
                element={
                    isLoggedIn ? <Navigate to="/metal-price" replace /> : <Login />
                }
            />

            {/* Protected routes */}
            <Route
                path="/*"
                element={
                    <ProtectedRoute>
                        <div className="flex h-screen overflow-hidden">
                            <div className={`fixed top-0 left-0 h-full transition-all duration-300 ${isSidebarOpen ? "w-72" : "w-0"}`}>
                                <Sidebar />
                            </div>
                            <div
                                className={`flex-grow flex flex-col min-h-screen w-full transition-all duration-300 ${
                                    isSidebarOpen ? "lg:ml-72" : "lg:ml-0"
                                }`}
                            >
                                <Header />
                                <div className="flex-1 overflow-y-auto">
                                    <Routes>
                                        <Route path="/" element={<Navigate to="/metal-price" replace />} />
                                        <Route path="/metal-price" element={<MetalPriceCalculation />} />
                                        <Route path="/making-charges" element={<MakingCharges />} />
                                        <Route path="/diamond-price" element={<DiamondPrice />} />
                                        <Route path="/solitaire-price" element={<SolitairePrice />} />
                                        <Route path="/gemstone-price" element={<GemstonePrice />}/>
                                        <Route path="/minimum-charges" element={<MinimumCharges />} />
                                        {/* 404 Route - Must be last */}
                                        <Route path="*" element={<NotFound />} />
                                    </Routes>
                                </div>
                            </div>
                        </div>
                    </ProtectedRoute>
                }
            />
        </Routes>
    );
};

export default Layout;
